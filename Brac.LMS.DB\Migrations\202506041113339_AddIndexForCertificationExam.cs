﻿namespace Brac.LMS.DB.Migrations
{
    using System;
    using System.Data.Entity.Migrations;

    public partial class AddIndexForCertificationExam : DbMigration
    {
        public override void Up()
        {
            // 1. MCQAnswers - Concurrency Check & Answer Retrieval
            // Used in: Duplicate submission check and answer loading
            // Query: WHERE x.TraineeExamId = @TraineeExamId
            Sql("CREATE NONCLUSTERED INDEX IX_MCQAnswer_TraineeExamId ON MCQAnswer (TraineeExamId) INCLUDE (QuestionId, Answered, Mark, EntryDate)");

            // 2. TrueFalseAnswers - Answer Processing
            // Used in: True/False answer operations
            // Query: WHERE x.TraineeExamId = @TraineeExamId
            Sql("CREATE NONCLUSTERED INDEX IX_TrueFalseAnswer_TraineeExamId ON TrueFalseAnswer (TraineeExamId) INCLUDE (QuestionId, Answered, Mark, EntryDate)");

            // 3. FIGAnswers - Fill in Gap Answer Processing
            // Used in: FIG answer operations
            // Query: WHERE x.TraineeExamId = @TraineeExamId
            Sql("CREATE NONCLUSTERED INDEX IX_FIGAnswer_TraineeExamId ON FIGAnswer (TraineeExamId) INCLUDE (QuestionId, Answered, Mark, EntryDate)");

            // 4. MatchingAnswers - Matching Question Processing
            // Used in: Matching answer operations
            // Query: WHERE x.TraineeExamId = @TraineeExamId
            Sql("CREATE NONCLUSTERED INDEX IX_MatchingAnswer_TraineeExamId ON MatchingAnswer (TraineeExamId) INCLUDE (QuestionId, RightSide, Mark, EntryDate)");

            // 5. WrittenAnswers - Written Question Processing
            // Used in: Written answer operations
            // Query: WHERE x.TraineeExamId = @TraineeExamId
            Sql("CREATE NONCLUSTERED INDEX IX_WrittenAnswer_TraineeExamId ON WrittenAnswer (TraineeExamId) INCLUDE (QuestionId, Answered, EntryDate)");

            // 6. TraineeExams - Exam Status & Completion Check
            // Used in: Previous completion check and exam retrieval
            // Query: WHERE x.TraineeId = @TraineeId AND x.ExamId = @ExamId
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeExam_TraineeId_ExamId ON TraineeExam (TraineeId, ExamId) INCLUDE (Status, GainedMarks, TotalMarks, StartDate, EndDate)");

            // 7. CourseExams - Exam Configuration Retrieval
            // Used in: Course exam data loading with includes
            // Query: WHERE x.Id = @ExamId
            Sql("CREATE NONCLUSTERED INDEX IX_CourseExam_Id_CourseId ON CourseExam (Id) INCLUDE (CourseId, Marks, MCQOnly, Publish, Quota)");

            // 8. MCQQuestions - Question Data Loading
            // Used in: Optimized question loading for answer processing
            // Query: WHERE x.Id IN (@QuestionIds)
            Sql("CREATE NONCLUSTERED INDEX IX_MCQQuestion_Id ON MCQQuestion (Id) INCLUDE (Answers, Mark)");

            // 9. TrueFalseQuestions - Question Data Loading
            // Used in: True/False question data retrieval
            // Query: WHERE x.Id IN (@QuestionIds)
            Sql("CREATE NONCLUSTERED INDEX IX_TrueFalseQuestion_Id ON TrueFalseQuestion (Id) INCLUDE (Answer, Mark)");

            // 10. FIGQuestions - Fill in Gap Question Loading
            // Used in: FIG question data retrieval
            // Query: WHERE x.Id IN (@QuestionIds)
            Sql("CREATE NONCLUSTERED INDEX IX_FIGQuestion_Id ON FIGQuestion (Id) INCLUDE (Answer, Mark)");

            // 11. MatchingQuestions - Matching Question Loading
            // Used in: Matching question data retrieval
            // Query: WHERE x.Id IN (@QuestionIds)
            Sql("CREATE NONCLUSTERED INDEX IX_MatchingQuestion_Id ON MatchingQuestion (Id) INCLUDE (RightSide, Mark)");

            // 12. WrittenQuestions - Written Question Loading
            // Used in: Written question data retrieval
            // Query: WHERE x.Id IN (@QuestionIds)
            Sql("CREATE NONCLUSTERED INDEX IX_WrittenQuestion_Id ON WrittenQuestion (Id) INCLUDE (Mark)");

            // 13. GradingPolicies - Grade Calculation
            // Used in: Grade assignment based on percentage
            // Query: WHERE x.Active = 1 AND x.MinValue <= @Percentage ORDER BY x.MinValue DESC
            Sql("CREATE NONCLUSTERED INDEX IX_GradingPolicy_Active_MinValue ON GradingPolicy (Active, MinValue DESC) INCLUDE (Id, GradeLetter, Result, GroupCode)");

            // 14. TraineeCertificates - Certificate Management
            // Used in: Certificate creation and updates
            // Query: WHERE x.Expired = 0 AND x.TraineeId = @TraineeId AND x.CourseId = @CourseId
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeCertificate_Expired_TraineeId_CourseId ON TraineeCertificate (Expired, TraineeId, CourseId) INCLUDE (Id, GainedMarks, TotalMarks, Grade, CertificateDate)");

            // 15. CertificateConfigurations - Latest Version Retrieval
            // Used in: Getting latest certificate configuration
            // Query: WHERE x.CourseId = @CourseId ORDER BY x.Version DESC
            Sql("CREATE NONCLUSTERED INDEX IX_CertificateConfiguration_CourseId_Version ON CertificateConfiguration (CourseId, Version DESC) INCLUDE (Id)");
        }

        public override void Down()
        {
            // Drop all certification exam performance indexes
            Sql("DROP INDEX IF EXISTS IX_MCQAnswer_TraineeExamId ON MCQAnswer");
            Sql("DROP INDEX IF EXISTS IX_TrueFalseAnswer_TraineeExamId ON TrueFalseAnswer");
            Sql("DROP INDEX IF EXISTS IX_FIGAnswer_TraineeExamId ON FIGAnswer");
            Sql("DROP INDEX IF EXISTS IX_MatchingAnswer_TraineeExamId ON MatchingAnswer");
            Sql("DROP INDEX IF EXISTS IX_WrittenAnswer_TraineeExamId ON WrittenAnswer");

            Sql("DROP INDEX IF EXISTS IX_TraineeExam_TraineeId_ExamId ON TraineeExam");
            Sql("DROP INDEX IF EXISTS IX_CourseExam_Id_CourseId ON CourseExam");

            Sql("DROP INDEX IF EXISTS IX_MCQQuestion_Id ON MCQQuestion");
            Sql("DROP INDEX IF EXISTS IX_TrueFalseQuestion_Id ON TrueFalseQuestion");
            Sql("DROP INDEX IF EXISTS IX_FIGQuestion_Id ON FIGQuestion");
            Sql("DROP INDEX IF EXISTS IX_MatchingQuestion_Id ON MatchingQuestion");
            Sql("DROP INDEX IF EXISTS IX_WrittenQuestion_Id ON WrittenQuestion");

            Sql("DROP INDEX IF EXISTS IX_GradingPolicy_Active_MinValue ON GradingPolicy");
            Sql("DROP INDEX IF EXISTS IX_TraineeCertificate_Expired_TraineeId_CourseId ON TraineeCertificate");
            Sql("DROP INDEX IF EXISTS IX_CertificateConfiguration_CourseId_Version ON CertificateConfiguration");
        }
    }
}
