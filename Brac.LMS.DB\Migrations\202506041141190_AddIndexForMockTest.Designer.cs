﻿// <auto-generated />
namespace Brac.LMS.DB.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.4.4")]
    public sealed partial class AddIndexForMockTest : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddIndexForMockTest));
        
        string IMigrationMetadata.Id
        {
            get { return "202506041141190_AddIndexForMockTest"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
